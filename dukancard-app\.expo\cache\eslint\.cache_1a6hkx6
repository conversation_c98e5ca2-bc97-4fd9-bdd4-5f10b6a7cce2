[{"C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx": "1", "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx": "2", "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx": "3", "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx": "4", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx": "5", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx": "6", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx": "7", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx": "8", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx": "9", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx": "10", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\likes.tsx": "11", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\reviews.tsx": "12", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\subscriptions.tsx": "13", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx": "14", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\edit-profile.tsx": "15", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx": "16", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx": "17", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx": "18", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx": "19", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx": "20", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx": "21", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "22", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx": "23", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\reviews.tsx": "24", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx": "25", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx": "26", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx": "27", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx": "28", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings.tsx": "29", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\subscriptions.tsx": "30", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx": "31", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx": "32", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx": "33", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx": "34", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx": "35", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx": "36", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx": "37", "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx": "38", "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx": "39", "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx": "40", "C:\\web-app\\dukancard-app\\app\\+not-found.tsx": "41", "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx": "42", "C:\\web-app\\dukancard-app\\app\\index.tsx": "43", "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx": "44", "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx": "45", "C:\\web-app\\dukancard-app\\app\\_layout.tsx": "46", "C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx": "47", "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx": "48", "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx": "49", "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx": "50", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx": "51", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx": "52", "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx": "53", "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx": "54", "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts": "55", "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx": "56", "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx": "57", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx": "58", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx": "59", "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx": "60", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx": "61", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx": "62", "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx": "63", "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx": "64", "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx": "65", "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx": "66", "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts": "67", "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts": "68", "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts": "69", "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts": "70", "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts": "71", "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts": "72", "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts": "73", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx": "74", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx": "75", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx": "76", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx": "77", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx": "78", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx": "79", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx": "80", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx": "81", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx": "82", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx": "83", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx": "84", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx": "85", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx": "86", "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx": "87", "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx": "88", "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx": "89", "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx": "90", "C:\\web-app\\dukancard-app\\src\\components\\index.ts": "91", "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx": "92", "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx": "93", "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts": "94", "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx": "95", "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx": "96", "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx": "97", "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx": "98", "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx": "99", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx": "100", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx": "101", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx": "102", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx": "103", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx": "104", "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx": "105", "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx": "106", "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx": "107", "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx": "108", "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx": "109", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx": "110", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx": "111", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx": "112", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx": "113", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx": "114", "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx": "115", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx": "116", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx": "117", "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx": "118", "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx": "119", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx": "120", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx": "121", "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx": "122", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx": "123", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx": "124", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx": "125", "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx": "126", "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx": "127", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx": "128", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx": "129", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts": "130", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx": "131", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx": "132", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx": "133", "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx": "134", "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx": "135", "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx": "136", "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx": "137", "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx": "138", "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx": "139", "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx": "140", "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx": "141", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx": "142", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx": "143", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AuthContainer.tsx": "144", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx": "145", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx": "146", "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts": "147", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx": "148", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx": "149", "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx": "150", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx": "151", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx": "152", "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts": "153", "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx": "154", "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts": "155", "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx": "156", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx": "157", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx": "158", "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts": "159", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx": "160", "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts": "161", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx": "162", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx": "163", "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts": "164", "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts": "165", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx": "166", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx": "167", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx": "168", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx": "169", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx": "170", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx": "171", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx": "172", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx": "173", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx": "174", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx": "175", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx": "176", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx": "177", "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts": "178", "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts": "179", "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts": "180", "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx": "181", "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx": "182", "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx": "183", "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx": "184", "C:\\web-app\\dukancard-app\\src\\contexts\\UserDataContext.tsx": "185", "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts": "186", "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts": "187", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts": "188", "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts": "189", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts": "190", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts": "191", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts": "192", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts": "193", "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts": "194", "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts": "195", "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts": "196", "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts": "197", "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts": "198", "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts": "199", "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts": "200", "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts": "201", "C:\\web-app\\dukancard-app\\src\\types\\ad.ts": "202", "C:\\web-app\\dukancard-app\\src\\types\\auth.ts": "203", "C:\\web-app\\dukancard-app\\src\\types\\components.ts": "204", "C:\\web-app\\dukancard-app\\src\\types\\index.ts": "205", "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts": "206", "C:\\web-app\\dukancard-app\\src\\types\\screens.ts": "207", "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts": "208", "C:\\web-app\\dukancard-app\\src\\types\\ui.ts": "209", "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts": "210", "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts": "211", "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts": "212", "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts": "213", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts": "214", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts": "215", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "216", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts": "217", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts": "218", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts": "219", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts": "220", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts": "221", "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts": "222", "C:\\web-app\\dukancard-app\\src\\utils\\index.ts": "223", "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts": "224", "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts": "225", "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts": "226", "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts": "227", "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts": "228", "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts": "229", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\activityService.ts": "230", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\index.ts": "231", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\adService.ts": "232", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\index.ts": "233", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\authService.ts": "234", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\emailOtpService.ts": "235", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\googleAuthService.ts": "236", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\index.ts": "237", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\mobileAuthService.ts": "238", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\nativeGoogleAuth2025.ts": "239", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessCardDataService.ts": "240", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessDiscovery.ts": "241", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessInteractions.ts": "242", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessOnboardingService.ts": "243", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessPostService.ts": "244", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessProfileService.ts": "245", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\index.ts": "246", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\index.ts": "247", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\metricsService.ts": "248", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\offlineService.ts": "249", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\onboardingService.ts": "250", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileCheckService.ts": "251", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileService.ts": "252", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\qrScanService.ts": "253", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\settingsService.ts": "254", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\syncService.ts": "255", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\userRoleStatusService.ts": "256", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\batchProfileService.ts": "257", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerPostService.ts": "258", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerProfileService.ts": "259", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\index.ts": "260", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\index.ts": "261", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\index.ts": "262", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\locationService.ts": "263", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\developmentErrorMonitoring.ts": "264", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\errorTracking.ts": "265", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\index.ts": "266", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\productionErrorLogging.ts": "267", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\index.ts": "268", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postInteractions.ts": "269", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postService.ts": "270", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\socialService.ts": "271", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\unifiedFeedService.ts": "272", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\index.ts": "273", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\productService.ts": "274", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\index.ts": "275", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\realtimeService.ts": "276", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\avatarUploadService.ts": "277", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\businessPostImageUploadService.ts": "278", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\customerPostImageUploadService.ts": "279", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\imageUploadService.ts": "280", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\index.ts": "281", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\storageService.ts": "282", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressUtils.ts": "283", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressValidation.ts": "284", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\businessSlugValidation.ts": "285", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\index.ts": "286", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\locationUtils.ts": "287", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\profileValidation.ts": "288", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\slugUtils.ts": "289", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\storage-paths.ts": "290", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\supabaseErrorHandler.ts": "291", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\validation.ts": "292", "C:\\web-app\\dukancard-app\\backend\\types\\activities.ts": "293", "C:\\web-app\\dukancard-app\\backend\\types\\auth.ts": "294", "C:\\web-app\\dukancard-app\\backend\\types\\business.ts": "295", "C:\\web-app\\dukancard-app\\backend\\types\\common.ts": "296", "C:\\web-app\\dukancard-app\\backend\\types\\customer.ts": "297", "C:\\web-app\\dukancard-app\\backend\\types\\index.ts": "298", "C:\\web-app\\dukancard-app\\backend\\types\\posts.ts": "299", "C:\\web-app\\dukancard-app\\backend\\types\\products.ts": "300", "C:\\web-app\\dukancard-app\\backend\\types\\storage.ts": "301", "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx": "302", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx": "303", "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx": "304", "C:\\web-app\\dukancard-app\\src\\components\\ui\\NetworkStatusBanner.tsx": "305", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts": "306", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx": "307", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx": "308", "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts": "309", "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkEmailSection.tsx": "310", "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkPhoneSection.tsx": "311", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordUpdateSection.tsx": "312"}, {"size": 10581, "mtime": 1751304355132, "results": "313", "hashOfConfig": "314"}, {"size": 33066, "mtime": 1751437289039, "results": "315", "hashOfConfig": "314"}, {"size": 22889, "mtime": 1751388114453, "results": "316", "hashOfConfig": "314"}, {"size": 3486, "mtime": 1751292266305, "results": "317", "hashOfConfig": "314"}, {"size": 2092, "mtime": 1751196935588, "results": "318", "hashOfConfig": "314"}, {"size": 3430, "mtime": 1751196935588, "results": "319", "hashOfConfig": "314"}, {"size": 9212, "mtime": 1751196935588, "results": "320", "hashOfConfig": "314"}, {"size": 2476, "mtime": 1751196935594, "results": "321", "hashOfConfig": "314"}, {"size": 14663, "mtime": 1751196935594, "results": "322", "hashOfConfig": "314"}, {"size": 2427, "mtime": 1751196935594, "results": "323", "hashOfConfig": "314"}, {"size": 7299, "mtime": 1751196935594, "results": "324", "hashOfConfig": "314"}, {"size": 8102, "mtime": 1751196935594, "results": "325", "hashOfConfig": "314"}, {"size": 7380, "mtime": 1751262018661, "results": "326", "hashOfConfig": "314"}, {"size": 5041, "mtime": 1751260878377, "results": "327", "hashOfConfig": "314"}, {"size": 6775, "mtime": 1751226611908, "results": "328", "hashOfConfig": "314"}, {"size": 6821, "mtime": 1751260028210, "results": "329", "hashOfConfig": "314"}, {"size": 9087, "mtime": 1751226387150, "results": "330", "hashOfConfig": "314"}, {"size": 6639, "mtime": 1751262057025, "results": "331", "hashOfConfig": "314"}, {"size": 7545, "mtime": 1751260191225, "results": "332", "hashOfConfig": "314"}, {"size": 6778, "mtime": 1751261637074, "results": "333", "hashOfConfig": "314"}, {"size": 2811, "mtime": 1751452003209, "results": "334", "hashOfConfig": "314"}, {"size": 6273, "mtime": 1751262096143, "results": "335", "hashOfConfig": "314"}, {"size": 17135, "mtime": 1751462886673, "results": "336", "hashOfConfig": "314"}, {"size": 8098, "mtime": 1751196935636, "results": "337", "hashOfConfig": "314"}, {"size": 7803, "mtime": 1751196935651, "results": "338", "hashOfConfig": "314"}, {"size": 2313, "mtime": 1751196935673, "results": "339", "hashOfConfig": "314"}, {"size": 5468, "mtime": 1751196935673, "results": "340", "hashOfConfig": "314"}, {"size": 2609, "mtime": 1751196935689, "results": "341", "hashOfConfig": "314"}, {"size": 3248, "mtime": 1751462880948, "results": "342", "hashOfConfig": "314"}, {"size": 7716, "mtime": 1751291766593, "results": "343", "hashOfConfig": "314"}, {"size": 2426, "mtime": 1751196935695, "results": "344", "hashOfConfig": "314"}, {"size": 659, "mtime": 1751196935700, "results": "345", "hashOfConfig": "314"}, {"size": 20650, "mtime": 1751442297742, "results": "346", "hashOfConfig": "314"}, {"size": 5864, "mtime": 1751430775268, "results": "347", "hashOfConfig": "314"}, {"size": 16496, "mtime": 1751392157784, "results": "348", "hashOfConfig": "314"}, {"size": 16310, "mtime": 1751442504979, "results": "349", "hashOfConfig": "314"}, {"size": 5897, "mtime": 1751224365126, "results": "350", "hashOfConfig": "314"}, {"size": 4763, "mtime": 1751227981990, "results": "351", "hashOfConfig": "314"}, {"size": 4632, "mtime": 1751228035517, "results": "352", "hashOfConfig": "314"}, {"size": 1909, "mtime": 1751469557670, "results": "353", "hashOfConfig": "314"}, {"size": 733, "mtime": 1751227293173, "results": "354", "hashOfConfig": "314"}, {"size": 5334, "mtime": 1751291766606, "results": "355", "hashOfConfig": "314"}, {"size": 992, "mtime": 1751344026984, "results": "356", "hashOfConfig": "314"}, {"size": 2546, "mtime": 1751228167665, "results": "357", "hashOfConfig": "314"}, {"size": 23758, "mtime": 1751228610414, "results": "358", "hashOfConfig": "314"}, {"size": 5093, "mtime": 1751344101856, "results": "359", "hashOfConfig": "314"}, {"size": 6483, "mtime": 1751196935876, "results": "360", "hashOfConfig": "314"}, {"size": 2348, "mtime": 1751389147247, "results": "361", "hashOfConfig": "314"}, {"size": 11982, "mtime": 1751198168666, "results": "362", "hashOfConfig": "314"}, {"size": 7406, "mtime": 1751196935889, "results": "363", "hashOfConfig": "314"}, {"size": 3489, "mtime": 1751196935889, "results": "364", "hashOfConfig": "314"}, {"size": 7684, "mtime": 1751202475903, "results": "365", "hashOfConfig": "314"}, {"size": 9891, "mtime": 1750575923377, "results": "366", "hashOfConfig": "314"}, {"size": 1282, "mtime": 1751198271726, "results": "367", "hashOfConfig": "314"}, {"size": 966, "mtime": 1750614134271, "results": "368", "hashOfConfig": "314"}, {"size": 11914, "mtime": 1751196935903, "results": "369", "hashOfConfig": "314"}, {"size": 10266, "mtime": 1751262337927, "results": "370", "hashOfConfig": "314"}, {"size": 6205, "mtime": 1751198271726, "results": "371", "hashOfConfig": "314"}, {"size": 9873, "mtime": 1751198271726, "results": "372", "hashOfConfig": "314"}, {"size": 6195, "mtime": 1751198271726, "results": "373", "hashOfConfig": "314"}, {"size": 8458, "mtime": 1751202488474, "results": "374", "hashOfConfig": "314"}, {"size": 16128, "mtime": 1751198271726, "results": "375", "hashOfConfig": "314"}, {"size": 2174, "mtime": 1751198271733, "results": "376", "hashOfConfig": "314"}, {"size": 1283, "mtime": 1751196935941, "results": "377", "hashOfConfig": "314"}, {"size": 5954, "mtime": 1751196935943, "results": "378", "hashOfConfig": "314"}, {"size": 737, "mtime": 1750575923371, "results": "379", "hashOfConfig": "314"}, {"size": 196, "mtime": 1751197002631, "results": "380", "hashOfConfig": "314"}, {"size": 163, "mtime": 1751197009882, "results": "381", "hashOfConfig": "314"}, {"size": 196, "mtime": 1751197016903, "results": "382", "hashOfConfig": "314"}, {"size": 394, "mtime": 1751196130079, "results": "383", "hashOfConfig": "314"}, {"size": 237, "mtime": 1751197673887, "results": "384", "hashOfConfig": "314"}, {"size": 218, "mtime": 1751197688254, "results": "385", "hashOfConfig": "314"}, {"size": 211, "mtime": 1751197701118, "results": "386", "hashOfConfig": "314"}, {"size": 3552, "mtime": 1751196935943, "results": "387", "hashOfConfig": "314"}, {"size": 16310, "mtime": 1751202047935, "results": "388", "hashOfConfig": "314"}, {"size": 17318, "mtime": 1751196935955, "results": "389", "hashOfConfig": "314"}, {"size": 3165, "mtime": 1751196935955, "results": "390", "hashOfConfig": "314"}, {"size": 11504, "mtime": 1751202056440, "results": "391", "hashOfConfig": "314"}, {"size": 12748, "mtime": 1751196935969, "results": "392", "hashOfConfig": "314"}, {"size": 4745, "mtime": 1751196935969, "results": "393", "hashOfConfig": "314"}, {"size": 853, "mtime": 1750575923388, "results": "394", "hashOfConfig": "314"}, {"size": 24364, "mtime": 1751259547993, "results": "395", "hashOfConfig": "314"}, {"size": 10083, "mtime": 1751196935984, "results": "396", "hashOfConfig": "314"}, {"size": 8134, "mtime": 1751196935989, "results": "397", "hashOfConfig": "314"}, {"size": 17474, "mtime": 1751196935997, "results": "398", "hashOfConfig": "314"}, {"size": 11033, "mtime": 1751196935997, "results": "399", "hashOfConfig": "314"}, {"size": 20468, "mtime": 1751196936008, "results": "400", "hashOfConfig": "314"}, {"size": 582, "mtime": 1750575923371, "results": "401", "hashOfConfig": "314"}, {"size": 859, "mtime": 1751196936010, "results": "402", "hashOfConfig": "314"}, {"size": 1491, "mtime": 1750612012115, "results": "403", "hashOfConfig": "314"}, {"size": 1443, "mtime": 1751198327172, "results": "404", "hashOfConfig": "314"}, {"size": 5537, "mtime": 1751196936016, "results": "405", "hashOfConfig": "314"}, {"size": 8573, "mtime": 1751196936022, "results": "406", "hashOfConfig": "314"}, {"size": 622, "mtime": 1750575923398, "results": "407", "hashOfConfig": "314"}, {"size": 7987, "mtime": 1751305336592, "results": "408", "hashOfConfig": "314"}, {"size": 4414, "mtime": 1751196936022, "results": "409", "hashOfConfig": "314"}, {"size": 8171, "mtime": 1751196936028, "results": "410", "hashOfConfig": "314"}, {"size": 11042, "mtime": 1751196936034, "results": "411", "hashOfConfig": "314"}, {"size": 2106, "mtime": 1751197247763, "results": "412", "hashOfConfig": "314"}, {"size": 9526, "mtime": 1751378320039, "results": "413", "hashOfConfig": "314"}, {"size": 6366, "mtime": 1751196936041, "results": "414", "hashOfConfig": "314"}, {"size": 9152, "mtime": 1751378320048, "results": "415", "hashOfConfig": "314"}, {"size": 4242, "mtime": 1750575923398, "results": "416", "hashOfConfig": "314"}, {"size": 2782, "mtime": 1751196936049, "results": "417", "hashOfConfig": "314"}, {"size": 10226, "mtime": 1751196936049, "results": "418", "hashOfConfig": "314"}, {"size": 1511, "mtime": 1750615669823, "results": "419", "hashOfConfig": "314"}, {"size": 9527, "mtime": 1751310828611, "results": "420", "hashOfConfig": "314"}, {"size": 4867, "mtime": 1751262377113, "results": "421", "hashOfConfig": "314"}, {"size": 11655, "mtime": 1751196936063, "results": "422", "hashOfConfig": "314"}, {"size": 11502, "mtime": 1751196936068, "results": "423", "hashOfConfig": "314"}, {"size": 12811, "mtime": 1751452002924, "results": "424", "hashOfConfig": "314"}, {"size": 8857, "mtime": 1751196936076, "results": "425", "hashOfConfig": "314"}, {"size": 7340, "mtime": 1751291766622, "results": "426", "hashOfConfig": "314"}, {"size": 5037, "mtime": 1751452002921, "results": "427", "hashOfConfig": "314"}, {"size": 2500, "mtime": 1751196936076, "results": "428", "hashOfConfig": "314"}, {"size": 9974, "mtime": 1751291766622, "results": "429", "hashOfConfig": "314"}, {"size": 16768, "mtime": 1751291766622, "results": "430", "hashOfConfig": "314"}, {"size": 6697, "mtime": 1751196936090, "results": "431", "hashOfConfig": "314"}, {"size": 12415, "mtime": 1751196936131, "results": "432", "hashOfConfig": "314"}, {"size": 12074, "mtime": 1751196936135, "results": "433", "hashOfConfig": "314"}, {"size": 14058, "mtime": 1751196936135, "results": "434", "hashOfConfig": "314"}, {"size": 2147, "mtime": 1751196936135, "results": "435", "hashOfConfig": "314"}, {"size": 4258, "mtime": 1750575923425, "results": "436", "hashOfConfig": "314"}, {"size": 3025, "mtime": 1751196936135, "results": "437", "hashOfConfig": "314"}, {"size": 4333, "mtime": 1751196936135, "results": "438", "hashOfConfig": "314"}, {"size": 2417, "mtime": 1751196936155, "results": "439", "hashOfConfig": "314"}, {"size": 717, "mtime": 1751196936155, "results": "440", "hashOfConfig": "314"}, {"size": 2325, "mtime": 1750575923425, "results": "441", "hashOfConfig": "314"}, {"size": 4452, "mtime": 1751196936169, "results": "442", "hashOfConfig": "314"}, {"size": 135, "mtime": 1751196936184, "results": "443", "hashOfConfig": "314"}, {"size": 2135, "mtime": 1750575923425, "results": "444", "hashOfConfig": "314"}, {"size": 7752, "mtime": 1751262412357, "results": "445", "hashOfConfig": "314"}, {"size": 1370, "mtime": 1751196936244, "results": "446", "hashOfConfig": "314"}, {"size": 6095, "mtime": 1750575923436, "results": "447", "hashOfConfig": "314"}, {"size": 11873, "mtime": 1751196936244, "results": "448", "hashOfConfig": "314"}, {"size": 2505, "mtime": 1750575923436, "results": "449", "hashOfConfig": "314"}, {"size": 6483, "mtime": 1750575923436, "results": "450", "hashOfConfig": "314"}, {"size": 4310, "mtime": 1750575923436, "results": "451", "hashOfConfig": "314"}, {"size": 6374, "mtime": 1750575923436, "results": "452", "hashOfConfig": "314"}, {"size": 803, "mtime": 1751196936309, "results": "453", "hashOfConfig": "314"}, {"size": 486, "mtime": 1751196936324, "results": "454", "hashOfConfig": "314"}, {"size": 8405, "mtime": 1751196936337, "results": "455", "hashOfConfig": "314"}, {"size": 1109, "mtime": 1750615180956, "results": "456", "hashOfConfig": "314"}, {"size": 1723, "mtime": 1751196936337, "results": "457", "hashOfConfig": "314"}, {"size": 7728, "mtime": 1751296181393, "results": "458", "hashOfConfig": "314"}, {"size": 2658, "mtime": 1751196936377, "results": "459", "hashOfConfig": "314"}, {"size": 139, "mtime": 1751197068957, "results": "460", "hashOfConfig": "314"}, {"size": 2949, "mtime": 1751196936377, "results": "461", "hashOfConfig": "314"}, {"size": 6869, "mtime": 1751196936377, "results": "462", "hashOfConfig": "314"}, {"size": 2385, "mtime": 1751196936377, "results": "463", "hashOfConfig": "314"}, {"size": 6605, "mtime": 1750575923441, "results": "464", "hashOfConfig": "314"}, {"size": 6960, "mtime": 1751196936390, "results": "465", "hashOfConfig": "314"}, {"size": 184, "mtime": 1751198341647, "results": "466", "hashOfConfig": "314"}, {"size": 8704, "mtime": 1751198271757, "results": "467", "hashOfConfig": "314"}, {"size": 147, "mtime": 1751197061209, "results": "468", "hashOfConfig": "314"}, {"size": 1119, "mtime": 1751177046067, "results": "469", "hashOfConfig": "314"}, {"size": 630, "mtime": 1750575923441, "results": "470", "hashOfConfig": "314"}, {"size": 1463, "mtime": 1750575923441, "results": "471", "hashOfConfig": "314"}, {"size": 214, "mtime": 1750575923459, "results": "472", "hashOfConfig": "314"}, {"size": 5440, "mtime": 1751307798433, "results": "473", "hashOfConfig": "314"}, {"size": 174, "mtime": 1751197077152, "results": "474", "hashOfConfig": "314"}, {"size": 4297, "mtime": 1751196936406, "results": "475", "hashOfConfig": "314"}, {"size": 4803, "mtime": 1751434095715, "results": "476", "hashOfConfig": "314"}, {"size": 97, "mtime": 1751197085063, "results": "477", "hashOfConfig": "314"}, {"size": 112, "mtime": 1751197092770, "results": "478", "hashOfConfig": "314"}, {"size": 2218, "mtime": 1750575923441, "results": "479", "hashOfConfig": "314"}, {"size": 5357, "mtime": 1751228764956, "results": "480", "hashOfConfig": "314"}, {"size": 6251, "mtime": 1751196936420, "results": "481", "hashOfConfig": "314"}, {"size": 4561, "mtime": 1750575923441, "results": "482", "hashOfConfig": "314"}, {"size": 3034, "mtime": 1751196936429, "results": "483", "hashOfConfig": "314"}, {"size": 3557, "mtime": 1751301373374, "results": "484", "hashOfConfig": "314"}, {"size": 17279, "mtime": 1751196936433, "results": "485", "hashOfConfig": "314"}, {"size": 2136, "mtime": 1751196936460, "results": "486", "hashOfConfig": "314"}, {"size": 566, "mtime": 1750575923441, "results": "487", "hashOfConfig": "314"}, {"size": 165, "mtime": 1750575923455, "results": "488", "hashOfConfig": "314"}, {"size": 5248, "mtime": 1751196936487, "results": "489", "hashOfConfig": "314"}, {"size": 8082, "mtime": 1751196936495, "results": "490", "hashOfConfig": "314"}, {"size": 2617, "mtime": 1751291766622, "results": "491", "hashOfConfig": "314"}, {"size": 929, "mtime": 1751301958057, "results": "492", "hashOfConfig": "314"}, {"size": 3021, "mtime": 1750575923461, "results": "493", "hashOfConfig": "314"}, {"size": 12242, "mtime": 1751440653354, "results": "494", "hashOfConfig": "314"}, {"size": 7168, "mtime": 1751202537764, "results": "495", "hashOfConfig": "314"}, {"size": 8309, "mtime": 1751378320055, "results": "496", "hashOfConfig": "314"}, {"size": 3053, "mtime": 1750575923462, "results": "497", "hashOfConfig": "314"}, {"size": 9925, "mtime": 1751469125706, "results": "498", "hashOfConfig": "314"}, {"size": 1882, "mtime": 1750575923462, "results": "499", "hashOfConfig": "314"}, {"size": 4833, "mtime": 1751196936564, "results": "500", "hashOfConfig": "314"}, {"size": 2162, "mtime": 1751196936564, "results": "501", "hashOfConfig": "314"}, {"size": 4524, "mtime": 1751196936580, "results": "502", "hashOfConfig": "314"}, {"size": 7836, "mtime": 1751196936582, "results": "503", "hashOfConfig": "314"}, {"size": 4167, "mtime": 1751203252731, "results": "504", "hashOfConfig": "314"}, {"size": 224, "mtime": 1751196936596, "results": "505", "hashOfConfig": "314"}, {"size": 501, "mtime": 1750575923462, "results": "506", "hashOfConfig": "314"}, {"size": 4891, "mtime": 1750575923462, "results": "507", "hashOfConfig": "314"}, {"size": 1791, "mtime": 1751259810669, "results": "508", "hashOfConfig": "314"}, {"size": 3652, "mtime": 1751432027356, "results": "509", "hashOfConfig": "314"}, {"size": 2447, "mtime": 1750600597419, "results": "510", "hashOfConfig": "314"}, {"size": 3083, "mtime": 1750575923472, "results": "511", "hashOfConfig": "314"}, {"size": 3949, "mtime": 1751378320055, "results": "512", "hashOfConfig": "314"}, {"size": 2356, "mtime": 1751196936607, "results": "513", "hashOfConfig": "314"}, {"size": 565, "mtime": 1751196936607, "results": "514", "hashOfConfig": "314"}, {"size": 390, "mtime": 1750577887298, "results": "515", "hashOfConfig": "314"}, {"size": 6615, "mtime": 1751468236607, "results": "516", "hashOfConfig": "314"}, {"size": 373, "mtime": 1751196958818, "results": "517", "hashOfConfig": "314"}, {"size": 299, "mtime": 1751196151030, "results": "518", "hashOfConfig": "314"}, {"size": 332, "mtime": 1751196948703, "results": "519", "hashOfConfig": "314"}, {"size": 286, "mtime": 1751196967098, "results": "520", "hashOfConfig": "314"}, {"size": 44187, "mtime": 1750575923568, "results": "521", "hashOfConfig": "314"}, {"size": 398, "mtime": 1751196979580, "results": "522", "hashOfConfig": "314"}, {"size": 4732, "mtime": 1750853448916, "results": "523", "hashOfConfig": "314"}, {"size": 5868, "mtime": 1750575923519, "results": "524", "hashOfConfig": "314"}, {"size": 4141, "mtime": 1751197392262, "results": "525", "hashOfConfig": "314"}, {"size": 8072, "mtime": 1751198271769, "results": "526", "hashOfConfig": "314"}, {"size": 3715, "mtime": 1750575923519, "results": "527", "hashOfConfig": "314"}, {"size": 5943, "mtime": 1750575923519, "results": "528", "hashOfConfig": "314"}, {"size": 9375, "mtime": 1750575923519, "results": "529", "hashOfConfig": "314"}, {"size": 134, "mtime": 1751197112565, "results": "530", "hashOfConfig": "314"}, {"size": 10403, "mtime": 1750575923519, "results": "531", "hashOfConfig": "314"}, {"size": 4149, "mtime": 1750575923519, "results": "532", "hashOfConfig": "314"}, {"size": 7483, "mtime": 1750575923519, "results": "533", "hashOfConfig": "314"}, {"size": 5633, "mtime": 1750575923519, "results": "534", "hashOfConfig": "314"}, {"size": 2746, "mtime": 1750575923519, "results": "535", "hashOfConfig": "314"}, {"size": 699, "mtime": 1751197435678, "results": "536", "hashOfConfig": "314"}, {"size": 6262, "mtime": 1751196936631, "results": "537", "hashOfConfig": "314"}, {"size": 1800, "mtime": 1750575923519, "results": "538", "hashOfConfig": "314"}, {"size": 3162, "mtime": 1751177046077, "results": "539", "hashOfConfig": "314"}, {"size": 4293, "mtime": 1751197405221, "results": "540", "hashOfConfig": "314"}, {"size": 1397, "mtime": 1750575923519, "results": "541", "hashOfConfig": "314"}, {"size": 2896, "mtime": 1750575923519, "results": "542", "hashOfConfig": "314"}, {"size": 14874, "mtime": 1751196935777, "results": "543", "hashOfConfig": "314"}, {"size": 115, "mtime": 1751196640840, "results": "544", "hashOfConfig": "314"}, {"size": 3467, "mtime": 1751196935782, "results": "545", "hashOfConfig": "314"}, {"size": 91, "mtime": 1751196674300, "results": "546", "hashOfConfig": "314"}, {"size": 1311, "mtime": 1751262212428, "results": "547", "hashOfConfig": "314"}, {"size": 3486, "mtime": 1750575923493, "results": "548", "hashOfConfig": "314"}, {"size": 4171, "mtime": 1750795292502, "results": "549", "hashOfConfig": "314"}, {"size": 196, "mtime": 1751202550804, "results": "550", "hashOfConfig": "314"}, {"size": 310, "mtime": 1751258874931, "results": "551", "hashOfConfig": "314"}, {"size": 7299, "mtime": 1751196935782, "results": "552", "hashOfConfig": "314"}, {"size": 18998, "mtime": 1751203029659, "results": "553", "hashOfConfig": "314"}, {"size": 7069, "mtime": 1751177046073, "results": "554", "hashOfConfig": "314"}, {"size": 10228, "mtime": 1750787828516, "results": "555", "hashOfConfig": "314"}, {"size": 12605, "mtime": 1751177046074, "results": "556", "hashOfConfig": "314"}, {"size": 2486, "mtime": 1751204700341, "results": "557", "hashOfConfig": "314"}, {"size": 8996, "mtime": 1750865742183, "results": "558", "hashOfConfig": "314"}, {"size": 244, "mtime": 1751202613508, "results": "559", "hashOfConfig": "314"}, {"size": 813, "mtime": 1751203054964, "results": "560", "hashOfConfig": "314"}, {"size": 5780, "mtime": 1750575923501, "results": "561", "hashOfConfig": "314"}, {"size": 13141, "mtime": 1750575923503, "results": "562", "hashOfConfig": "314"}, {"size": 9121, "mtime": 1751196935796, "results": "563", "hashOfConfig": "314"}, {"size": 8958, "mtime": 1750866379202, "results": "564", "hashOfConfig": "314"}, {"size": 10184, "mtime": 1750575923503, "results": "565", "hashOfConfig": "314"}, {"size": 14026, "mtime": 1751202770312, "results": "566", "hashOfConfig": "314"}, {"size": 9347, "mtime": 1750575923509, "results": "567", "hashOfConfig": "314"}, {"size": 10267, "mtime": 1751196935809, "results": "568", "hashOfConfig": "314"}, {"size": 9824, "mtime": 1750870230820, "results": "569", "hashOfConfig": "314"}, {"size": 9929, "mtime": 1751291766606, "results": "570", "hashOfConfig": "314"}, {"size": 1514, "mtime": 1751204815410, "results": "571", "hashOfConfig": "314"}, {"size": 3873, "mtime": 1751262975209, "results": "572", "hashOfConfig": "314"}, {"size": 408, "mtime": 1751203069163, "results": "573", "hashOfConfig": "314"}, {"size": 857, "mtime": 1751198050893, "results": "574", "hashOfConfig": "314"}, {"size": 112, "mtime": 1751196649882, "results": "575", "hashOfConfig": "314"}, {"size": 5305, "mtime": 1751262177734, "results": "576", "hashOfConfig": "314"}, {"size": 6017, "mtime": 1750741154161, "results": "577", "hashOfConfig": "314"}, {"size": 4903, "mtime": 1750741251063, "results": "578", "hashOfConfig": "314"}, {"size": 199, "mtime": 1751196683686, "results": "579", "hashOfConfig": "314"}, {"size": 7591, "mtime": 1750741228357, "results": "580", "hashOfConfig": "314"}, {"size": 141, "mtime": 1751196622657, "results": "581", "hashOfConfig": "314"}, {"size": 6390, "mtime": 1750575923503, "results": "582", "hashOfConfig": "314"}, {"size": 241, "mtime": 1751204860041, "results": "583", "hashOfConfig": "314"}, {"size": 12362, "mtime": 1751203084783, "results": "584", "hashOfConfig": "314"}, {"size": 872, "mtime": 1751204892055, "results": "585", "hashOfConfig": "314"}, {"size": 110, "mtime": 1751196631522, "results": "586", "hashOfConfig": "314"}, {"size": 2484, "mtime": 1751228547509, "results": "587", "hashOfConfig": "314"}, {"size": 122, "mtime": 1751196661692, "results": "588", "hashOfConfig": "314"}, {"size": 16096, "mtime": 1750575923509, "results": "589", "hashOfConfig": "314"}, {"size": 11977, "mtime": 1751291766606, "results": "590", "hashOfConfig": "314"}, {"size": 4914, "mtime": 1751197366255, "results": "591", "hashOfConfig": "314"}, {"size": 4883, "mtime": 1751197379381, "results": "592", "hashOfConfig": "314"}, {"size": 9401, "mtime": 1751196935835, "results": "593", "hashOfConfig": "314"}, {"size": 608, "mtime": 1751202926556, "results": "594", "hashOfConfig": "314"}, {"size": 10131, "mtime": 1751291766606, "results": "595", "hashOfConfig": "314"}, {"size": 571, "mtime": 1751197793865, "results": "596", "hashOfConfig": "314"}, {"size": 5386, "mtime": 1750575923519, "results": "597", "hashOfConfig": "314"}, {"size": 4723, "mtime": 1750865308830, "results": "598", "hashOfConfig": "314"}, {"size": 1088, "mtime": 1751202969783, "results": "599", "hashOfConfig": "314"}, {"size": 1017, "mtime": 1751177046076, "results": "600", "hashOfConfig": "314"}, {"size": 5163, "mtime": 1750575923519, "results": "601", "hashOfConfig": "314"}, {"size": 745, "mtime": 1751197805491, "results": "602", "hashOfConfig": "314"}, {"size": 6793, "mtime": 1750575923519, "results": "603", "hashOfConfig": "314"}, {"size": 765, "mtime": 1751197817016, "results": "604", "hashOfConfig": "314"}, {"size": 8425, "mtime": 1750575923519, "results": "605", "hashOfConfig": "314"}, {"size": 320, "mtime": 1751197763583, "results": "606", "hashOfConfig": "314"}, {"size": 386, "mtime": 1751197716471, "results": "607", "hashOfConfig": "314"}, {"size": 522, "mtime": 1751291766606, "results": "608", "hashOfConfig": "314"}, {"size": 402, "mtime": 1751197782239, "results": "609", "hashOfConfig": "314"}, {"size": 338, "mtime": 1751197734938, "results": "610", "hashOfConfig": "314"}, {"size": 481, "mtime": 1751196097814, "results": "611", "hashOfConfig": "314"}, {"size": 396, "mtime": 1751197746117, "results": "612", "hashOfConfig": "314"}, {"size": 375, "mtime": 1751197754372, "results": "613", "hashOfConfig": "314"}, {"size": 382, "mtime": 1751197772875, "results": "614", "hashOfConfig": "314"}, {"size": 2704, "mtime": 1751380574388, "results": "615", "hashOfConfig": "314"}, {"size": 8236, "mtime": 1751306237260, "results": "616", "hashOfConfig": "314"}, {"size": 5286, "mtime": 1751306252227, "results": "617", "hashOfConfig": "314"}, {"size": 6168, "mtime": 1751310451393, "results": "618", "hashOfConfig": "314"}, {"size": 7726, "mtime": 1751310592239, "results": "619", "hashOfConfig": "314"}, {"size": 2683, "mtime": 1751380496260, "results": "620", "hashOfConfig": "314"}, {"size": 3755, "mtime": 1751309969653, "results": "621", "hashOfConfig": "314"}, {"size": 7437, "mtime": 1751436144849, "results": "622", "hashOfConfig": "314"}, {"size": 8184, "mtime": 1751469617869, "results": "623", "hashOfConfig": "314"}, {"size": 3075, "mtime": 1751462923580, "results": "624", "hashOfConfig": "314"}, {"size": 6742, "mtime": 1751469330188, "results": "625", "hashOfConfig": "314"}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "64mijf", {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1538", "messages": "1539", "suppressedMessages": "1540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1541", "messages": "1542", "suppressedMessages": "1543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1544", "messages": "1545", "suppressedMessages": "1546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1547", "messages": "1548", "suppressedMessages": "1549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1550", "messages": "1551", "suppressedMessages": "1552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1553", "messages": "1554", "suppressedMessages": "1555", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1556", "messages": "1557", "suppressedMessages": "1558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1559", "messages": "1560", "suppressedMessages": "1561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\likes.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\reviews.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\subscriptions.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\edit-profile.tsx", [], ["1562"], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\reviews.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\subscriptions.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx", [], ["1563"], "C:\\web-app\\dukancard-app\\app\\+not-found.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx", [], ["1564", "1565"], "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AuthContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\UserDataContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ad.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\auth.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\components.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\screens.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ui.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\activityService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\adService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\authService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\emailOtpService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\googleAuthService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\mobileAuthService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\nativeGoogleAuth2025.ts", [], ["1566"], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessCardDataService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessDiscovery.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessOnboardingService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessPostService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessProfileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\metricsService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\offlineService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\onboardingService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileCheckService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\qrScanService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\settingsService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\syncService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\userRoleStatusService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\batchProfileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerPostService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerProfileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\locationService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\developmentErrorMonitoring.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\errorTracking.ts", [], ["1567"], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\productionErrorLogging.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\socialService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\unifiedFeedService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\productService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\realtimeService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\avatarUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\businessPostImageUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\customerPostImageUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\imageUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\storageService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressValidation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\businessSlugValidation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\locationUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\profileValidation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\slugUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\supabaseErrorHandler.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\validation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\activities.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\auth.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\business.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\common.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\customer.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\posts.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\products.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\storage.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\NetworkStatusBanner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkEmailSection.tsx", ["1568"], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordUpdateSection.tsx", [], [], {"ruleId": "1569", "severity": 1, "message": "1570", "line": 30, "column": 6, "nodeType": "1571", "endLine": 30, "endColumn": 8, "suggestions": "1572", "suppressions": "1573"}, {"ruleId": "1574", "severity": 2, "message": "1575", "line": 8, "column": 36, "nodeType": "1576", "endLine": 8, "endColumn": 69, "suppressions": "1577"}, {"ruleId": "1569", "severity": 1, "message": "1578", "line": 469, "column": 6, "nodeType": "1571", "endLine": 469, "endColumn": 56, "suggestions": "1579", "suppressions": "1580"}, {"ruleId": "1569", "severity": 1, "message": "1581", "line": 498, "column": 6, "nodeType": "1571", "endLine": 498, "endColumn": 100, "suggestions": "1582", "suppressions": "1583"}, {"ruleId": "1584", "severity": 1, "message": "1585", "line": 12, "column": 30, "nodeType": "1586", "messageId": "1587", "endLine": 12, "endColumn": 82, "suppressions": "1588"}, {"ruleId": "1584", "severity": 1, "message": "1585", "line": 54, "column": 33, "nodeType": "1586", "messageId": "1587", "endLine": 54, "endColumn": 89, "suppressions": "1589"}, {"ruleId": "1590", "severity": 2, "message": "1591", "line": 136, "column": 17, "nodeType": "1592", "messageId": "1593", "suggestions": "1594"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCustomerProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["1595"], ["1596"], "import/no-unresolved", "Unable to resolve path to module '@/components/useClientOnlyValue'.", "Literal", ["1597"], "React Hook useCallback has missing dependencies: 'styles.loadingFooter' and 'styles.loadingText'. Either include them or remove the dependency array.", ["1598"], ["1599"], "React Hook useCallback has missing dependencies: 'styles.emptyContainer', 'styles.emptySubtitle', 'styles.emptyTitle', and 'styles.skeletonContainer'. Either include them or remove the dependency array.", ["1600"], ["1601"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["1602"], ["1603"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1604", "1605", "1606", "1607"], {"desc": "1608", "fix": "1609"}, {"kind": "1610", "justification": "1611"}, {"kind": "1610", "justification": "1611"}, {"desc": "1612", "fix": "1613"}, {"kind": "1610", "justification": "1611"}, {"desc": "1614", "fix": "1615"}, {"kind": "1610", "justification": "1611"}, {"kind": "1610", "justification": "1611"}, {"kind": "1610", "justification": "1611"}, {"messageId": "1616", "data": "1617", "fix": "1618", "desc": "1619"}, {"messageId": "1616", "data": "1620", "fix": "1621", "desc": "1622"}, {"messageId": "1616", "data": "1623", "fix": "1624", "desc": "1625"}, {"messageId": "1616", "data": "1626", "fix": "1627", "desc": "1628"}, "Update the dependencies array to be: [fetchCustomerProfile]", {"range": "1629", "text": "1630"}, "directive", "", "Update the dependencies array to be: [isLoading, hasMore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", {"range": "1631", "text": "1632"}, "Update the dependencies array to be: [isLoadingFromCache, posts.length, isLoading, styles.skeletonContainer, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", {"range": "1633", "text": "1634"}, "replaceWithAlt", {"alt": "1635"}, {"range": "1636", "text": "1637"}, "Replace with `&apos;`.", {"alt": "1638"}, {"range": "1639", "text": "1640"}, "Replace with `&lsquo;`.", {"alt": "1641"}, {"range": "1642", "text": "1643"}, "Replace with `&#39;`.", {"alt": "1644"}, {"range": "1645", "text": "1646"}, "Replace with `&rsquo;`.", [1050, 1052], "[fetchCustomerProfile]", [16022, 16072], "[isLoading, has<PERSON>ore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", [17059, 17153], "[isLoading<PERSON><PERSON><PERSON><PERSON>, posts.length, isLoading, styles.skeletonContainer, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", "&apos;", [4754, 4798], "\n              We&apos;ve sent a 6-digit code to ", "&lsquo;", [4754, 4798], "\n              We&lsquo;ve sent a 6-digit code to ", "&#39;", [4754, 4798], "\n              We&#39;ve sent a 6-digit code to ", "&rsquo;", [4754, 4798], "\n              We&rsquo;ve sent a 6-digit code to "]