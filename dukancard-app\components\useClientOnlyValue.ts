import { useState, useEffect } from 'react';

/**
 * Returns the value only on the client side, and undefined on the server side.
 * This is useful for preventing hydration mismatches when using values that are only available on the client.
 * 
 * @param clientValue - The value to return on the client side
 * @param serverValue - The value to return on the server side (defaults to undefined)
 * @returns The appropriate value based on the environment
 */
export function useClientOnlyValue<T>(clientValue: T, serverValue?: T): T | undefined {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient ? clientValue : serverValue;
}
